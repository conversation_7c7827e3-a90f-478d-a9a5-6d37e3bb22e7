import networkx as nx
import numpy as np
import time


class GEN_GRAPH:
    """Network graph generator and neighbor cache container

    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """

    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存

        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）

        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）

        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）

        Returns:
            生成的NetworkX无向图对象

        Raises:
            FileNotFoundError: 当文件路径不存在时
            ValueError: 当数据列不足或无法转换为整数时
            RuntimeError: 当图构建过程中出现未知错误时
        """
        try:
            G = nx.Graph()
            # 使用numpy加速边数据加载（跳过首行表头）
            edges_data = np.loadtxt(
                self.network_path,
                skiprows=1,    # 忽略标题行
                usecols=[0, 1] # 仅读取前两列作为边
            )
            # 将浮点数据转换为整数节点ID（适用于非负整数ID）
            edges = [(int(u), int(v)) for u, v in edges_data]
            G.add_edges_from(edges)
            return G
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Network file not found: {self.network_path}") from e
        except ValueError as e:
            raise ValueError(f"Invalid data format in {self.network_path}") from e
        except Exception as e:
            raise RuntimeError(f"Graph construction failed: {str(e)}") from e

def optimized_EDV(graph, S, p):
    """
    优化后的影响力评估 (Expected Diffusion Value) - BP近似

    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率

    Returns:
        float: 估计的影响力值
    """
    # 预生成邻接字典（将邻居存储为集合）
    adj_dict = graph.neighbors
    S = set(S)

    # 计算一阶邻居 (NS_1)，直接使用集合操作
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S

    # 快速计算每个邻居的连接数
    influence_sum = 0
    for node in NS_1:
        num_connections = len(set(adj_dict[node]) & S)
        influence_sum += 1 - (1 - p) ** num_connections

    return len(S) + influence_sum


def EDV_Neumann(G, S, p, alpha=0.1, K=3):
    """
    Neumann级数修正的BP近似影响力估计函数
    Args:
        G: 网络X图对象
        S: 种子节点集合
        p: 传播概率
        alpha: Neumann修正强度因子
        K: Neumann级数截断阶数
    Returns:
        float: 估计影响力值
    """
    S = set(S)
    nodes = list(G.nodes())
    idx_map = {v: i for i, v in enumerate(nodes)}
    n = len(nodes)

    # 构建传播矩阵 A
    A = np.zeros((n, n))
    for u, v in G.edges():
        A[idx_map[u], idx_map[v]] = p
        A[idx_map[v], idx_map[u]] = p  # 若为无向图

    # 初始化激活概率向量
    P = np.array([1.0 if v in S else 0.0 for v in nodes])

    max_hop = 4
    history = [P.copy()]

    for t in range(1, max_hop + 1):
        new_P = P.copy()
        for i, v in enumerate(nodes):
            if v in S:
                continue
            parents = list(G.neighbors(v))
            prob_not = 1.0
            for u in parents:
                prob_not *= 1 - p * P[idx_map[u]]
            new_P[i] = 1 - prob_not
        P = new_P
        history.append(P.copy())

        # Neumann级数修正
        correction = np.zeros_like(P)
        for k in range(2, min(K + 1, len(history))):
            Ak = np.linalg.matrix_power(A, k)
            correction += Ak @ history[-k]
        P += alpha * correction
        P = np.clip(P, 0, 1)  # 保证概率合法

    return float(np.sum(P))


def IC_vec(g, seed, p, mc=1000):
    """向量化实现的独立级联模型模拟

    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: 蒙特卡洛模拟次数

    Returns:
        float: 平均影响力扩散范围
    """
    # 预处理节点到索引的映射和邻居列表
    node_list = list(g.nodes())
    node_to_index = {node: idx for idx, node in enumerate(node_list)}
    n = len(node_list)

    # 预存每个节点的邻居索引数组（向量化预生成）
    preprocessed_neighbors = [
        np.array([node_to_index[n] for n in g.neighbors(node)], dtype=np.int32)
        for node in node_list
    ]

    # 转换种子节点为索引
    seed_indices = np.array([node_to_index[n] for n in seed], dtype=np.int32)

    # 预分配内存空间
    influence = np.empty(mc, dtype=np.int32)

    for i in range(mc):
        active = np.zeros(n, dtype=np.bool_)
        active[seed_indices] = True
        newly_active = active.copy()

        while np.any(newly_active):
            # 向量化收集激活节点的所有邻居
            current_active = np.flatnonzero(newly_active)
            neighbors = np.concatenate([preprocessed_neighbors[idx] for idx in current_active])

            if neighbors.size == 0:
                break

            # 生成与neighbors长度匹配的随机数
            rand_values = np.random.rand(len(neighbors))  # 直接生成所需长度的随机数

            # 向量化筛选激活节点
            activated = neighbors[rand_values < p]
            unique_activated = np.unique(activated)

            # 更新激活状态
            is_new = ~active[unique_activated]
            newly_active_nodes = unique_activated[is_new]

            active[newly_active_nodes] = True
            newly_active.fill(False)
            newly_active[newly_active_nodes] = True

        influence[i] = active.sum()

    return np.mean(influence)




def main():
    network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    G = GEN_GRAPH(network_path)
    p = 0.05
    k = 100  # 设置要选择的顶点数量

    # 直接在main函数中计算度中心性并选择前k个节点
    degree_centrality = nx.degree_centrality(G.nx_G)
    sorted_nodes = sorted(degree_centrality.items(),
                         key=lambda x: x[1],
                         reverse=True)
    top_k_nodes = [node for node, _ in sorted_nodes[:k]]

    # 计算BP近似并统计时间
    start_time = time.time()
    bp_result = optimized_EDV(G, top_k_nodes, p)
    bp_time = time.time() - start_time

    # 计算BP Neumann修正并统计时间
    start_time = time.time()
    bp_neumann_result = EDV_Neumann(G.nx_G, top_k_nodes, p, alpha=0.1, K=3)
    bp_neumann_time = time.time() - start_time

    # 计算IC模型模拟结果
    start_time = time.time()
    ic_result = IC_vec(G.nx_G, top_k_nodes, p, mc=10000)
    ic_time = time.time() - start_time

    # 打印计算结果和时间统计
    print("计算结果和时间统计:")
    print(f"BP近似: {bp_result} (耗时: {bp_time:.10f}秒)")
    print(f"BP Neumann修正: {bp_neumann_result} (耗时: {bp_neumann_time:.10f}秒)")
    print(f"IC模型模拟 (10000次): {ic_result} (耗时: {ic_time:.10f}秒)")

    # 与IC模拟的准确度比较
    print("\n各评估函数与IC模拟的相对误差:")
    if ic_result > 0:
        print(f"BP近似相对误差: {abs((bp_result - ic_result)/ic_result)*100:.2f}%")
        print(f"BP Neumann修正相对误差: {abs((bp_neumann_result - ic_result)/ic_result)*100:.2f}%")

    # 计算性能提升
    print("\n性能分析:")
    print(f"BP近似相比IC模拟速度提升: {(ic_time/bp_time):.2f}倍")
    print(f"BP Neumann修正相比IC模拟速度提升: {(ic_time/bp_neumann_time):.2f}倍")
    print(f"BP Neumann修正相比BP近似精度提升: {abs((bp_neumann_result - ic_result) - (bp_result - ic_result)):.2f}")

    # 测试不同参数对BP Neumann的影响
    print("\n不同参数下的BP Neumann结果:")
    for alpha in [0.05, 0.1, 0.2]:
        for K in [2, 3, 5]:
            start_time = time.time()
            result = EDV_Neumann(G.nx_G, top_k_nodes, p, alpha=alpha, K=K)
            elapsed_time = time.time() - start_time
            error = abs((result - ic_result)/ic_result)*100 if ic_result > 0 else 0
            print(f"  alpha={alpha}, K={K}: {result:.2f} (误差: {error:.2f}%, 耗时: {elapsed_time:.6f}秒)")

if __name__ == "__main__":
    main()